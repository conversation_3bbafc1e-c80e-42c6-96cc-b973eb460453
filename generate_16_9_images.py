#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> 16:9 pre horror audio súbory
Podporuje rôzne metódy generovania obrázkov
"""

import os
import sys
import json
import requests
from PIL import Image, ImageDraw, ImageFont
import random

# Farby pre výstup
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    NC = '\033[0m'

def print_color(color, text):
    print(f"{color}{text}{Colors.NC}")

def create_gradient_background(width, height, color1, color2, direction='horizontal'):
    """Vytvorí gradientné pozadie"""
    image = Image.new('RGB', (width, height))
    draw = ImageDraw.Draw(image)
    
    if direction == 'horizontal':
        for x in range(width):
            ratio = x / width
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            draw.line([(x, 0), (x, height)], fill=(r, g, b))
    else:  # vertical
        for y in range(height):
            ratio = y / height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    return image

def create_horror_background(width, height, style='dark_red'):
    """Vytvorí horror pozadie"""
    if style == 'dark_red':
        return create_gradient_background(width, height, (26, 0, 0), (51, 0, 0), 'vertical')
    elif style == 'blood':
        return create_gradient_background(width, height, (13, 0, 0), (102, 0, 0), 'horizontal')
    elif style == 'midnight':
        return create_gradient_background(width, height, (0, 0, 13), (13, 0, 26), 'vertical')
    elif style == 'forest':
        return create_gradient_background(width, height, (0, 13, 0), (0, 26, 13), 'horizontal')
    else:
        # Čierne pozadie
        return Image.new('RGB', (width, height), (0, 0, 0))

def add_text_overlay(image, title, subtitle=""):
    """Pridá text na obrázok"""
    draw = ImageDraw.Draw(image)
    width, height = image.size
    
    try:
        # Pokus o načítanie systémového fontu
        title_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 72)
        subtitle_font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 36)
    except:
        # Fallback na default font
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # Výpočet pozície textu
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_height = title_bbox[3] - title_bbox[1]
    
    title_x = (width - title_width) // 2
    title_y = height // 2 - title_height
    
    # Tieň textu
    shadow_offset = 3
    draw.text((title_x + shadow_offset, title_y + shadow_offset), title, 
              font=title_font, fill=(0, 0, 0, 128))
    
    # Hlavný text
    draw.text((title_x, title_y), title, font=title_font, fill=(255, 255, 255))
    
    if subtitle:
        subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (width - subtitle_width) // 2
        subtitle_y = title_y + title_height + 20
        
        # Tieň podtitulku
        draw.text((subtitle_x + shadow_offset, subtitle_y + shadow_offset), subtitle, 
                  font=subtitle_font, fill=(0, 0, 0, 128))
        draw.text((subtitle_x, subtitle_y), subtitle, font=subtitle_font, fill=(200, 200, 200))
    
    return image

def generate_openai_image(prompt, api_key, size="1792x1024"):
    """Generuje obrázok pomocou OpenAI DALL-E API"""
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "dall-e-3",
        "prompt": prompt,
        "n": 1,
        "size": size,
        "quality": "standard"
    }
    
    try:
        response = requests.post(
            "https://api.openai.com/v1/images/generations",
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            image_url = result['data'][0]['url']
            
            # Stiahnutie obrázka
            img_response = requests.get(image_url, timeout=30)
            if img_response.status_code == 200:
                return img_response.content
        else:
            print_color(Colors.RED, f"OpenAI API chyba: {response.status_code}")
            print_color(Colors.YELLOW, response.text)
            
    except Exception as e:
        print_color(Colors.RED, f"Chyba pri generovaní obrázka: {e}")
    
    return None

def create_prompt_from_filename(filename):
    """Vytvorí prompt pre AI na základe názvu súboru"""
    # Odstránenie prípony a emoji
    clean_name = filename.replace('.mp3', '').replace('🔪', '').strip()
    
    # Základný horror prompt
    base_prompt = "Dark atmospheric horror scene, cinematic lighting, gothic style, "
    
    # Špecifické prompty na základe názvu
    if 'upír' in clean_name.lower() or 'vampire' in clean_name.lower():
        return base_prompt + "vampire castle, blood moon, gothic architecture, dark red atmosphere"
    elif 'les' in clean_name.lower() or 'forest' in clean_name.lower():
        return base_prompt + "dark haunted forest, twisted trees, fog, moonlight through branches"
    elif 'hrad' in clean_name.lower() or 'castle' in clean_name.lower():
        return base_prompt + "ancient haunted castle, storm clouds, lightning, gothic towers"
    elif 'duch' in clean_name.lower() or 'ghost' in clean_name.lower():
        return base_prompt + "ghostly apparition, abandoned mansion, ethereal mist, supernatural"
    elif 'mlyn' in clean_name.lower() or 'mill' in clean_name.lower():
        return base_prompt + "old abandoned mill, waterwheel, dark water, eerie atmosphere"
    else:
        return base_prompt + f"mysterious dark scene inspired by '{clean_name}', horror atmosphere, gothic elements"

def main():
    print_color(Colors.PURPLE, "=== Generátor obrázkov 16:9 pre Horror Audio ===")
    
    # Nastavenia
    INPUT_DIR = os.path.expanduser("~/Desktop/Krvavý Audio")
    OUTPUT_DIR = "./Horror_Images_16_9"
    WIDTH = 1920
    HEIGHT = 1080
    
    # Vytvorenie výstupného priečinka
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    print_color(Colors.YELLOW, f"Vstupný priečinok: {INPUT_DIR}")
    print_color(Colors.YELLOW, f"Výstupný priečinok: {OUTPUT_DIR}")
    print_color(Colors.YELLOW, f"Rozlíšenie: {WIDTH}x{HEIGHT} (16:9)")
    print()
    
    # Menu pre typ generovania
    print_color(Colors.BLUE, "Vyberte metódu generovania obrázkov:")
    print("1) Jednoduché gradientné pozadia (rýchle)")
    print("2) Pozadia s textom (názov príbehu)")
    print("3) OpenAI DALL-E generovanie (potrebný API kľúč)")
    print("4) Kombinácia - gradient + text")
    print()
    
    choice = input("Zadajte číslo (1-4): ").strip()
    
    # Pre OpenAI API
    api_key = None
    if choice == '3':
        api_key = input("Zadajte OpenAI API kľúč: ").strip()
        if not api_key:
            print_color(Colors.RED, "API kľúč je povinný pre OpenAI generovanie!")
            return
    
    # Získanie zoznamu MP3 súborov
    mp3_files = []
    if os.path.exists(INPUT_DIR):
        mp3_files = [f for f in os.listdir(INPUT_DIR) if f.endswith('.mp3')]
    
    if not mp3_files:
        print_color(Colors.RED, f"Nenašli sa žiadne MP3 súbory v {INPUT_DIR}")
        return
    
    print_color(Colors.GREEN, f"Našiel som {len(mp3_files)} MP3 súborov")
    print()
    
    # Spracovanie súborov
    for i, mp3_file in enumerate(mp3_files, 1):
        filename_base = mp3_file.replace('.mp3', '')
        output_file = os.path.join(OUTPUT_DIR, f"{filename_base}.png")
        
        print_color(Colors.YELLOW, f"[{i}/{len(mp3_files)}] {filename_base}")
        
        if choice == '1':
            # Jednoduché gradientné pozadie
            styles = ['dark_red', 'blood', 'midnight', 'forest']
            style = random.choice(styles)
            image = create_horror_background(WIDTH, HEIGHT, style)
            
        elif choice == '2':
            # Pozadie s textom
            image = create_horror_background(WIDTH, HEIGHT, 'dark_red')
            image = add_text_overlay(image, filename_base)
            
        elif choice == '3':
            # OpenAI DALL-E
            prompt = create_prompt_from_filename(mp3_file)
            print_color(Colors.BLUE, f"  Prompt: {prompt[:60]}...")
            
            image_data = generate_openai_image(prompt, api_key)
            if image_data:
                with open(output_file, 'wb') as f:
                    f.write(image_data)
                print_color(Colors.GREEN, "  ✓ OpenAI obrázok vygenerovaný")
                continue
            else:
                print_color(Colors.RED, "  ✗ Chyba pri generovaní, používam fallback")
                image = create_horror_background(WIDTH, HEIGHT, 'dark_red')
                image = add_text_overlay(image, filename_base)
                
        elif choice == '4':
            # Kombinácia
            styles = ['dark_red', 'blood', 'midnight']
            style = random.choice(styles)
            image = create_horror_background(WIDTH, HEIGHT, style)
            image = add_text_overlay(image, filename_base, "Horror Príbeh")
            
        else:
            print_color(Colors.RED, "Neplatná voľba, používam základné pozadie")
            image = create_horror_background(WIDTH, HEIGHT, 'dark_red')
        
        # Uloženie obrázka
        if choice != '3':  # Pre OpenAI už je uložený
            image.save(output_file, 'PNG', quality=95)
            print_color(Colors.GREEN, f"  ✓ Uložené: {os.path.basename(output_file)}")
    
    print()
    print_color(Colors.GREEN, "=== Generovanie obrázkov dokončené! ===")
    print_color(Colors.YELLOW, f"Obrázky nájdete v: {OUTPUT_DIR}")
    print_color(Colors.BLUE, "Teraz môžete použiť tieto obrázky vo video konvertore!")

if __name__ == "__main__":
    main()
