#!/bin/bash

# Test konverzie jedného MP3 súboru na video

# Farby
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

# Nastavenia
INPUT_FILE="$HOME/Desktop/Krvavý Audio/🔪 Bezruká Príbeh prekliateho mlyna 🔪.mp3"
OUTPUT_DIR="./Test_Video"
OUTPUT_FILE="$OUTPUT_DIR/Bezruka_test.mp4"
RESOLUTION="1920x1080"
BG_COLOR="0x1a0000"  # Tmavo červená

# Vytvorenie výstupného priečinka
mkdir -p "$OUTPUT_DIR"

print_color $GREEN "=== Test konverzie jedného súboru ==="
print_color $YELLOW "Vstup: $(basename "$INPUT_FILE")"
print_color $YELLOW "Výstup: $OUTPUT_FILE"
print_color $BLUE "Spúšťam konverziu..."

# Test konverzie
ffmpeg -i "$INPUT_FILE" \
       -f lavfi -i "color=c=$BG_COLOR:size=$RESOLUTION:rate=25" \
       -c:v libx264 -c:a aac \
       -shortest \
       -t 30 \
       -y "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    print_color $GREEN "✓ Test úspešný!"
    print_color $YELLOW "Video (30 sekúnd) vytvorené: $OUTPUT_FILE"
    
    # Zobrazenie informácií o súbore
    echo ""
    print_color $BLUE "Informácie o vytvorenom videu:"
    ffprobe -v quiet -print_format json -show_format -show_streams "$OUTPUT_FILE" | grep -E '"duration"|"width"|"height"|"codec_name"' | head -6
else
    print_color $RED "✗ Test neúspešný!"
fi
