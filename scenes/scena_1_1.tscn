[gd_scene load_steps=3 format=3 uid="uid://bvxvqjqtqwqxr"]

[ext_resource type="Script" path="res://scripts/scena_1_1_simple.gd" id="1_0hdqv"]

[sub_resource type="GradientTexture2D" id="GradientTexture2D_1"]
gradient = SubResource("Gradient_1")
width = 1080
height = 1920

[sub_resource type="Gradient" id="Gradient_1"]
colors = PackedColorArray(0.1, 0.05, 0.2, 1, 0.05, 0.02, 0.1, 1)

[node name="Scena_1_1" type="Node2D"]
script = ExtResource("1_0hdqv")

[node name="Pozadie" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.08, 0.04, 0.15, 1)

[node name="TestLabel" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = -100.0
text = "PREKLIATEHO DEDIČSTVO
Scéna 1.1 - Kočiar v noci"
horizontal_alignment = 1
vertical_alignment = 1

[node name="InfoLabel" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = 300.0
offset_right = 250.0
offset_bottom = 400.0
text = "Kočiar je vytvorený z Godot primitívov
Lampa bliká pomocí AnimationPlayer"
horizontal_alignment = 1
vertical_alignment = 1

[node name="StartButton" type="Button" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = 450.0
offset_right = 100.0
offset_bottom = 500.0
text = "Spustiť hádanku"

[node name="Kociar_Grafika" type="Node2D" parent="."]

[node name="Kociar_Telo" type="ColorRect" parent="Kociar_Grafika"]
offset_left = 240.0
offset_top = 860.0
offset_right = 840.0
offset_bottom = 1160.0
color = Color(0.3, 0.2, 0.1, 1)

[node name="Kociar_Strecha" type="ColorRect" parent="Kociar_Grafika"]
offset_left = 260.0
offset_top = 810.0
offset_right = 820.0
offset_bottom = 860.0
color = Color(0.2, 0.15, 0.08, 1)

[node name="Koleso_Lava" type="ColorRect" parent="Kociar_Grafika"]
offset_left = 340.0
offset_top = 1140.0
offset_right = 420.0
offset_bottom = 1220.0
color = Color(0.4, 0.3, 0.2, 1)

[node name="Koleso_Prava" type="ColorRect" parent="Kociar_Grafika"]
offset_left = 660.0
offset_top = 1140.0
offset_right = 740.0
offset_bottom = 1220.0
color = Color(0.4, 0.3, 0.2, 1)

[node name="Lampa" type="ColorRect" parent="Kociar_Grafika"]
offset_left = 220.0
offset_top = 910.0
offset_right = 260.0
offset_bottom = 1010.0
color = Color(1, 0.8, 0.4, 1)

[node name="Lampa_Svetlo" type="ColorRect" parent="Kociar_Grafika/Lampa"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 20.0
offset_right = -5.0
offset_bottom = -20.0
color = Color(1, 1, 0.6, 0.8)

[node name="AnimationPlayer" type="AnimationPlayer" parent="Kociar_Grafika/Lampa"]
autoplay = "blikanie"
libraries = {
"": SubResource("AnimationLibrary_1")
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
"blikanie": SubResource("Animation_1")
}

[sub_resource type="Animation" id="Animation_1"]
resource_name = "blikanie"
length = 2.0
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Lampa_Svetlo:color")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.5, 1, 1.5),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Color(1, 1, 0.6, 0.8), Color(1, 1, 0.6, 0.3), Color(1, 1, 0.6, 0.9), Color(1, 1, 0.6, 0.4)]
}

[node name="Blesk" type="ColorRect" parent="."]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(1, 1, 1, 0)

[node name="Zvuk_Ambient" type="AudioStreamPlayer" parent="."]
autoplay = true
bus = &"Master"

[node name="Zvuk_Efekty" type="AudioStreamPlayer" parent="."]
bus = &"Master"

[node name="UI_Hlavolam" type="CanvasLayer" parent="."]
visible = false

[node name="List" type="Panel" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -300.0
offset_right = 200.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2

[node name="List_Text" type="RichTextLabel" parent="UI_Hlavolam/List"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
bbcode_enabled = true
text = "[center][color=black][font_size=24]Tajomný List[/font_size][/color][/center]

[color=darkblue]Milý čitateľ,

Ak čítaš tento list, znamená to, že si našiel cestu do môjho kočiara. Nie je to náhoda.

Riešenie hádanky je skryté v slovách:
[b]\"Grófka je v krypte\"[/b]

Ale pozor - musíš to napísať ako jedno slovo, bez medzier a diakritiky.[/color]"

[node name="VstupnePole" type="LineEdit" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = 120.0
offset_right = 150.0
offset_bottom = 160.0
grow_horizontal = 2
grow_vertical = 2
placeholder_text = "Zadajte odpoveď..."

[node name="TlacitkoOverit" type="Button" parent="UI_Hlavolam"]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -60.0
offset_top = 180.0
offset_right = 60.0
offset_bottom = 220.0
grow_horizontal = 2
grow_vertical = 2
text = "Overiť"

[connection signal="pressed" from="UI_Hlavolam/TlacitkoOverit" to="." method="_on_tlacitko_overit_pressed"]
[connection signal="pressed" from="StartButton" to="." method="_on_start_button_pressed"]
