#!/bin/bash

# Jednoduchý generátor 16:9 pozadí pomocou FFmpeg
# Pre rýchle vytvorenie základných pozadí

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

# Kontrola FFmpeg
if ! command -v ffmpeg &> /dev/null; then
    print_color $RED "FFmpeg nie je nainštalovaný!"
    exit 1
fi

# Nastavenia
OUTPUT_DIR="./Simple_Backgrounds_16_9"
WIDTH=1920
HEIGHT=1080

# Vytvorenie výstupného priečinka
mkdir -p "$OUTPUT_DIR"

print_color $PURPLE "=== Generátor jednoduchých 16:9 pozadí ==="
print_color $YELLOW "Výstupný priečinok: $OUTPUT_DIR"
print_color $YELLOW "Rozlíšenie: ${WIDTH}x${HEIGHT}"
echo ""

# Menu pre typ pozadia
print_color $BLUE "Vyberte typ pozadia:"
echo "1) Tmavo červené (horror)"
echo "2) Čierne (klasické)"
echo "3) Tmavo modré (mystické)"
echo "4) Tmavo zelené (les)"
echo "5) Gradient červeno-čierny"
echo "6) Gradient modro-čierny"
echo "7) Všetky typy (6 rôznych pozadí)"
echo ""
read -p "Zadajte číslo (1-7): " choice

create_background() {
    local name="$1"
    local color="$2"
    local output_file="$OUTPUT_DIR/${name}_background_16_9.png"
    
    print_color $YELLOW "Vytváram: $name"
    
    ffmpeg -f lavfi -i "color=c=$color:size=${WIDTH}x${HEIGHT}:rate=1" \
           -frames:v 1 \
           -y "$output_file" \
           -loglevel error
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ $name vytvorené"
    else
        print_color $RED "✗ Chyba pri vytváraní $name"
    fi
}

create_gradient() {
    local name="$1"
    local color1="$2"
    local color2="$3"
    local direction="$4"
    local output_file="$OUTPUT_DIR/${name}_gradient_16_9.png"
    
    print_color $YELLOW "Vytváram gradient: $name"
    
    if [ "$direction" = "horizontal" ]; then
        filter="linear_gradient=x0=0:y0=0:x1=${WIDTH}:y1=0:color1=${color1}:color2=${color2}"
    else
        filter="linear_gradient=x0=0:y0=0:x1=0:y1=${HEIGHT}:color1=${color1}:color2=${color2}"
    fi
    
    ffmpeg -f lavfi -i "color=c=black:size=${WIDTH}x${HEIGHT}:rate=1" \
           -vf "$filter" \
           -frames:v 1 \
           -y "$output_file" \
           -loglevel error
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ $name gradient vytvorený"
    else
        print_color $RED "✗ Chyba pri vytváraní $name gradient"
        # Fallback na jednofarebné pozadie
        create_background "$name" "$color1"
    fi
}

case $choice in
    1)
        create_background "dark_red" "0x1a0000"
        ;;
    2)
        create_background "black" "0x000000"
        ;;
    3)
        create_background "dark_blue" "0x000033"
        ;;
    4)
        create_background "dark_green" "0x001a00"
        ;;
    5)
        create_gradient "red_black" "0x330000" "0x000000" "vertical"
        ;;
    6)
        create_gradient "blue_black" "0x000033" "0x000000" "vertical"
        ;;
    7)
        print_color $BLUE "Vytváram všetky typy pozadí..."
        create_background "dark_red" "0x1a0000"
        create_background "black" "0x000000"
        create_background "dark_blue" "0x000033"
        create_background "dark_green" "0x001a00"
        create_gradient "red_black" "0x330000" "0x000000" "vertical"
        create_gradient "blue_black" "0x000033" "0x000000" "vertical"
        ;;
    *)
        print_color $RED "Neplatná voľba, vytváram tmavo červené pozadie"
        create_background "dark_red" "0x1a0000"
        ;;
esac

echo ""
print_color $GREEN "=== Pozadia vytvorené! ==="
print_color $YELLOW "Súbory nájdete v: $OUTPUT_DIR"
print_color $BLUE "Tieto pozadia môžete použiť vo video konvertore s možnosťou 6 (vlastný obrázok)"

# Zobrazenie vytvorených súborov
echo ""
print_color $PURPLE "Vytvorené súbory:"
ls -la "$OUTPUT_DIR"/*.png 2>/dev/null | while read line; do
    filename=$(echo "$line" | awk '{print $9}')
    if [ -n "$filename" ]; then
        basename_file=$(basename "$filename")
        print_color $BLUE "  • $basename_file"
    fi
done
