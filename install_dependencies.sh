#!/bin/bash

# Inštalácia závislostí pre Gradio Midjourney generátor

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

print_color $BLUE "=== Inštalácia závislostí pre Gradio Midjourney ==="
echo ""

# Kontrola Python3
if ! command -v python3 &> /dev/null; then
    print_color $RED "Python3 nie je nainštalovaný!"
    print_color $YELLOW "Nainštalujte Python3 a spustite znovu."
    exit 1
fi

print_color $GREEN "✓ Python3 nájdený: $(python3 --version)"

# Kontrola pip
if ! command -v pip3 &> /dev/null; then
    print_color $RED "pip3 nie je nainštalovaný!"
    exit 1
fi

print_color $GREEN "✓ pip3 nájdený"

# Inštalácia závislostí
print_color $BLUE "Inštalujem potrebné Python balíčky..."

# Základné závislosti
pip3 install gradio_client requests Pillow

if [ $? -eq 0 ]; then
    print_color $GREEN "✓ Závislosti úspešne nainštalované!"
else
    print_color $RED "✗ Chyba pri inštalácii závislostí"
    exit 1
fi

# Test importu
print_color $BLUE "Testujem importy..."
python3 -c "
try:
    from gradio_client import Client
    import requests
    from PIL import Image
    print('✓ Všetky moduly sa dajú importovať')
except ImportError as e:
    print(f'✗ Chyba pri importe: {e}')
    exit(1)
"

if [ $? -eq 0 ]; then
    print_color $GREEN "✓ Všetko je pripravené!"
    print_color $YELLOW "Teraz môžete spustiť:"
    print_color $BLUE "python3 gradio_midjourney_generator.py"
else
    print_color $RED "✗ Problém s importmi"
fi
