#!/bin/bash

# MP3 to Video Converter
# Konvertuje MP3 súbory na videá s rôznymi možnosťami pozadia

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcia pre výpis farebného textu
print_color() {
    echo -e "${1}${2}${NC}"
}

# Kontrola či je nainštalovaný FFmpeg
check_ffmpeg() {
    if ! command -v ffmpeg &> /dev/null; then
        print_color $RED "FFmpeg nie je nainštalovaný!"
        print_color $YELLOW "Na macOS ho môžete nainštalovať pomocou: brew install ffmpeg"
        exit 1
    fi
}

# Funkcia pre vytvorenie videa s farebným pozadím
create_video_with_color() {
    local input_file="$1"
    local output_file="$2"
    local color="$3"
    local resolution="$4"
    
    print_color $BLUE "Vytváram video s farebným pozadím: $color"
    
    ffmpeg -i "$input_file" \
           -f lavfi -i "color=c=$color:size=$resolution:rate=25" \
           -c:v libx264 -c:a aac \
           -shortest \
           -y "$output_file"
}

# Funkcia pre vytvorenie videa so spektrogramom
create_video_with_spectrum() {
    local input_file="$1"
    local output_file="$2"
    local resolution="$3"
    
    print_color $BLUE "Vytváram video so spektrogramom"
    
    ffmpeg -i "$input_file" \
           -filter_complex "[0:a]showspectrum=s=$resolution:mode=combined:color=rainbow:scale=log,format=yuv420p[v]" \
           -map "[v]" -map 0:a \
           -c:v libx264 -c:a aac \
           -y "$output_file"
}

# Funkcia pre vytvorenie videa s waveform
create_video_with_waveform() {
    local input_file="$1"
    local output_file="$2"
    local resolution="$3"
    local bg_color="$4"
    
    print_color $BLUE "Vytváram video s waveform"
    
    ffmpeg -i "$input_file" \
           -filter_complex "[0:a]showwaves=s=$resolution:mode=line:colors=white,colorkey=0x000000:0.01:0.1,format=yuv420p[waves];color=c=$bg_color:s=$resolution[bg];[bg][waves]overlay[v]" \
           -map "[v]" -map 0:a \
           -c:v libx264 -c:a aac \
           -y "$output_file"
}

# Funkcia pre vytvorenie videa s obrázkom na pozadí
create_video_with_image() {
    local input_file="$1"
    local output_file="$2"
    local image_file="$3"
    local resolution="$4"
    
    print_color $BLUE "Vytváram video s obrázkom na pozadí"
    
    ffmpeg -loop 1 -i "$image_file" -i "$input_file" \
           -c:v libx264 -c:a aac \
           -vf "scale=$resolution:force_original_aspect_ratio=decrease,pad=$resolution:(ow-iw)/2:(oh-ih)/2" \
           -shortest \
           -y "$output_file"
}

# Hlavná funkcia
main() {
    print_color $GREEN "=== MP3 to Video Converter ==="
    
    # Kontrola FFmpeg
    check_ffmpeg
    
    # Nastavenia
    INPUT_DIR="$HOME/Desktop/Krvavý Audio"
    OUTPUT_DIR="./Krvavý_Audio_Videos"
    RESOLUTION="1920x1080"  # Full HD
    
    # Vytvorenie výstupného priečinka
    mkdir -p "$OUTPUT_DIR"
    
    print_color $YELLOW "Vstupný priečinok: $INPUT_DIR"
    print_color $YELLOW "Výstupný priečinok: $OUTPUT_DIR"
    print_color $YELLOW "Rozlíšenie: $RESOLUTION"
    
    # Menu pre výber typu videa
    echo ""
    print_color $BLUE "Vyberte typ videa:"
    echo "1) Farebné pozadie (čierne)"
    echo "2) Farebné pozadie (tmavo červené)"
    echo "3) Spektrogram (dúhové farby)"
    echo "4) Waveform (biele vlny na tmavom pozadí)"
    echo "5) Waveform (červené vlny na čiernom pozadí)"
    echo "6) Vlastný obrázok na pozadí"
    echo "7) Auto-použitie vygenerovaných 16:9 obrázkov"
    echo "8) Náhodné pozadia z Simple_Backgrounds_16_9"
    echo ""
    read -p "Zadajte číslo (1-8): " choice
    
    # Spracovanie všetkých MP3 súborov
    count=0
    total=$(find "$INPUT_DIR" -name "*.mp3" | wc -l)
    
    print_color $GREEN "Našiel som $total MP3 súborov na spracovanie..."
    echo ""
    
    find "$INPUT_DIR" -name "*.mp3" | while read -r mp3_file; do
        count=$((count + 1))
        filename=$(basename "$mp3_file" .mp3)
        output_file="$OUTPUT_DIR/${filename}.mp4"
        
        print_color $YELLOW "[$count/$total] Spracovávam: $filename"
        
        case $choice in
            1)
                create_video_with_color "$mp3_file" "$output_file" "black" "$RESOLUTION"
                ;;
            2)
                create_video_with_color "$mp3_file" "$output_file" "darkred" "$RESOLUTION"
                ;;
            3)
                create_video_with_spectrum "$mp3_file" "$output_file" "$RESOLUTION"
                ;;
            4)
                create_video_with_waveform "$mp3_file" "$output_file" "$RESOLUTION" "black"
                ;;
            5)
                create_video_with_waveform "$mp3_file" "$output_file" "$RESOLUTION" "black"
                ;;
            6)
                read -p "Zadajte cestu k obrázku: " image_path
                if [ -f "$image_path" ]; then
                    create_video_with_image "$mp3_file" "$output_file" "$image_path" "$RESOLUTION"
                else
                    print_color $RED "Obrázok neexistuje, používam čierne pozadie"
                    create_video_with_color "$mp3_file" "$output_file" "black" "$RESOLUTION"
                fi
                ;;
            7)
                # Auto-použitie vygenerovaných obrázkov
                filename_base=$(basename "$mp3_file" .mp3)
                generated_image="./Horror_Images_16_9/${filename_base}.png"
                if [ -f "$generated_image" ]; then
                    create_video_with_image "$mp3_file" "$output_file" "$generated_image" "$RESOLUTION"
                else
                    print_color $YELLOW "Obrázok pre $filename_base neexistuje, používam tmavo červené pozadie"
                    create_video_with_color "$mp3_file" "$output_file" "darkred" "$RESOLUTION"
                fi
                ;;
            8)
                # Náhodné pozadia
                bg_dir="./Simple_Backgrounds_16_9"
                if [ -d "$bg_dir" ]; then
                    bg_files=("$bg_dir"/*.png)
                    if [ ${#bg_files[@]} -gt 0 ] && [ -f "${bg_files[0]}" ]; then
                        random_bg="${bg_files[$RANDOM % ${#bg_files[@]}]}"
                        create_video_with_image "$mp3_file" "$output_file" "$random_bg" "$RESOLUTION"
                    else
                        print_color $YELLOW "Žiadne pozadia v $bg_dir, používam tmavo červené"
                        create_video_with_color "$mp3_file" "$output_file" "darkred" "$RESOLUTION"
                    fi
                else
                    print_color $YELLOW "Priečinok $bg_dir neexistuje, používam tmavo červené pozadie"
                    create_video_with_color "$mp3_file" "$output_file" "darkred" "$RESOLUTION"
                fi
                ;;
            *)
                print_color $RED "Neplatná voľba, používam čierne pozadie"
                create_video_with_color "$mp3_file" "$output_file" "black" "$RESOLUTION"
                ;;
        esac
        
        if [ $? -eq 0 ]; then
            print_color $GREEN "✓ Úspešne vytvorené: ${filename}.mp4"
        else
            print_color $RED "✗ Chyba pri vytváraní: ${filename}.mp4"
        fi
        echo ""
    done
    
    print_color $GREEN "=== Konverzia dokončená! ==="
    print_color $YELLOW "Videá sa nachádzajú v priečinku: $OUTPUT_DIR"
}

# Spustenie hlavnej funkcie
main "$@"
