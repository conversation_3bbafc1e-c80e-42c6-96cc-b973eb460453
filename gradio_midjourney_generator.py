#!/usr/bin/env python3
"""
Generátor o<PERSON> pomocou Gradio Midjourney API
Vytvorí obrázky pre horror audio súbory a následne videá
"""

import os
import sys
import time
import requests
from gradio_client import Client

# Farby pre výstup
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_color(color, text):
    print(f"{color}{text}{Colors.NC}")

def create_horror_prompt(filename):
    """Vytvorí špecifický horror prompt na základe názvu súboru"""
    # Odstránenie prípony a emoji
    clean_name = filename.replace('.mp3', '').replace('🔪', '').strip()

    # Základný horror prompt s 16:9 špecifikáciou
    base_prompt = "cinematic horror scene, dark atmospheric, gothic style, 16:9 aspect ratio, high quality, detailed, professional lighting, "

    # Špecifické prompty pre konkrétne príbehy
    prompts_map = {
        "Bezruká Príbeh prekliateho mlyna": "cursed watermill at night, ghostly figure without hands, dark flowing water, ancient stone mill wheel, eerie mist, supernatural curse, gothic horror atmosphere",
        "#VIY666": "demonic entity VIY, ancient Slavic horror, underground crypt, glowing red eyes, dark ritual symbols, occult atmosphere, supernatural terror",
        "Ako nám rozprávky pomáhajú byť samými sebou": "mystical fairy tale book, glowing pages, magical forest, psychological symbolism, dream-like atmosphere, surreal elements",
        "Baba Jaga": "Baba Yaga's hut on chicken legs, dark Russian forest, iron teeth witch, bone fence, skull lanterns, Slavic folklore horror",
        "Bábika": "possessed antique doll, cracked porcelain face, empty staring eyes, Victorian dollhouse, supernatural possession, creepy toy horror",
        "Babylon 333": "ancient Babylon ruins, mystical ziggurats, occult symbols, dark rituals, demonic presence, biblical horror, apocalyptic atmosphere",
        "Barbar": "savage warrior in dark forest, primitive horror, tribal masks, ancient brutality, blood-soaked battlefield, barbaric rituals",
        "Bielovodská dolina": "haunted Slovak valley, misty mountains, abandoned village, ghostly apparitions, Carpathian folklore, supernatural mystery",
        "Bohynky, Grgalica a Strigy": "Slavic mythological creatures, forest spirits, shape-shifting demons, ancient pagan rituals, supernatural entities",
        "Bratislava": "gothic Bratislava castle at night, Danube river, medieval streets, urban horror, historical darkness, Slovak gothic atmosphere",
        "Či jesto pravda na svete": "philosophical horror, reality distortion, surreal nightmare, existential dread, truth vs illusion, psychological terror",
        "Čierna voda": "black cursed water, dark lake surface, drowning spirits, water demons, reflection horror, aquatic supernatural terror",
        "Čo bolo na starej páske": "vintage cassette tape, static noise, ghostly recordings, retro horror, haunted audio equipment, supernatural playback",
        "Čo je to Mora": "sleep paralysis demon Mora, nightmare creature, bedroom horror, suffocating presence, Slavic night terror, supernatural oppression",
        "Čo je to Zmok": "Slovak dragon Zmok, ancient serpent, treasure guardian, cave dwelling monster, medieval beast, mythological horror",
        "Dedina do zeme prepadnutá": "village swallowed by earth, sinkhole horror, buried houses, underground terror, geological nightmare, lost civilization",
        "Démon v kapustnom poli": "demon in cabbage field, rural horror, agricultural nightmare, possessed farmland, countryside terror, harvest horror",
        "Diera v dome": "mysterious hole in house, portal to darkness, architectural horror, void in reality, supernatural opening, dimensional terror",
        "Dojímavý a strašidelný príbeh z hradu Modrý Kameň": "Blue Stone Castle, Slovak medieval fortress, tragic ghost story, castle ruins, historical horror",
        "Draqwlya": "Dracula vampire, Transylvanian castle, blood moon, gothic horror, undead aristocrat, vampire lord, classic horror atmosphere",
        "Duchárske príbehy": "ghost stories, spectral apparitions, haunted locations, supernatural encounters, spirit manifestations, paranormal activity",
        "Dybbuk": "Jewish folklore demon, possessed soul, religious horror, ancient curse, spiritual possession, mystical terror",
        "Entita": "supernatural entity, unknown presence, paranormal investigation, invisible horror, unexplained phenomena, otherworldly being",
        "Fotky smrti": "death photographs, cursed images, supernatural photography, haunted pictures, visual horror, deadly snapshots",
        "Halíčsky kaštieľ": "Halič manor house, aristocratic horror, abandoned mansion, noble family curse, historical gothic atmosphere",
        "Hanička bez rúk": "Hanička without hands, mutilation horror, tragic folklore, disability curse, Slovak folk horror, body horror",
        "Hanigovský hrad": "Hanigov castle ruins, medieval fortress, castle ghost, historical horror, Slovak gothic architecture",
        "HMLA": "thick supernatural fog, visibility horror, lost in mist, atmospheric terror, weather phenomenon horror, disorientation nightmare",
        "Horor v čase korony": "pandemic horror, coronavirus terror, medical nightmare, isolation horror, disease fear, modern apocalyptic atmosphere",
        "Horory zo Šíravy": "Šírava lake horror, water monster, drowning terror, lakeside nightmare, aquatic horror, Slovak lake legends",
        "Hrôza za dedinou": "terror beyond village, rural isolation, countryside horror, remote location terror, agricultural nightmare",
        "Irenka": "possessed woman Irenka, female horror, supernatural possession, psychological terror, personal nightmare, intimate horror",
        "Jure Grando": "historical vampire Jure Grando, Istrian vampire, undead resurrection, medieval horror, documented vampire case",
        "Kreslo": "cursed armchair, furniture horror, possessed object, domestic terror, household nightmare, supernatural furniture",
        "Laktibrada": "bearded demon, facial hair horror, grotesque creature, Slavic folklore monster, supernatural beard entity",
        "Les šepká tvoje meno": "whispering forest, trees calling name, woodland horror, nature supernatural, forest consciousness, sylvan terror",
        "Lilith": "demon Lilith, first woman, biblical horror, feminine demon, night terror, ancient evil, supernatural seductress",
        "Loď duchov": "ghost ship, maritime horror, spectral vessel, oceanic nightmare, nautical supernatural, phantom boat",
        "Malý chlapec": "possessed little boy, child horror, innocent evil, supernatural child, demonic possession, childhood nightmare",
        "Miladina hostina": "Milada's feast, cannibalistic dinner, human consumption, dining horror, feast of flesh, culinary nightmare",
        "Mlynárova dcéra": "miller's daughter, watermill horror, rural folklore, grinding wheel terror, flour dust nightmare, mill ghost",
        "Mlynček": "little mill, miniature horror, toy terror, small object curse, domestic nightmare, household supernatural",
        "Modrofúz": "blue beard demon, facial hair horror, color-coded terror, supernatural beard entity, Slavic creature",
        "Môj otec bol už raz ženatý": "father's previous marriage, family secrets, domestic horror, marital nightmare, household terror",
        "Mŕtvy frajer": "dead boyfriend, romantic horror, relationship nightmare, undead lover, supernatural romance, death and love",
        "Mŕtvy strýko": "dead uncle, family horror, relative ghost, household supernatural, familial nightmare, ancestral terror",
        "Mužský narcizmus - Vlkolak": "male narcissism werewolf, psychological horror, transformation terror, masculine monster, lycanthrope psychology",
        "Na Babej na Babej": "Baba mountain horror, Slovak peak terror, mountainous nightmare, alpine supernatural, highland horror",
        "Návrat zo záhrobia": "return from beyond, resurrection horror, undead comeback, afterlife terror, supernatural return, death reversal",
        "Nechytajte zlodeja": "don't catch the thief, criminal horror, pursuit nightmare, law enforcement terror, chase horror",
        "Neuveriteľný prípad Andreja Závodského": "incredible case of Andrej Závodský, investigative horror, mysterious case, supernatural investigation",
        "Newportský duch": "Newport ghost, American horror, transatlantic supernatural, foreign spirit, international nightmare",
        "Niečo v rohu": "something in corner, spatial horror, room terror, corner nightmare, domestic supernatural, household fear",
        "Nočná cesta": "night journey, travel horror, dark road, nocturnal nightmare, transportation terror, midnight voyage",
        "Nočný teror": "night terror, sleep horror, bedroom nightmare, nocturnal fear, sleeping disorder, dream terror",
        "Noku": "mysterious entity Noku, unknown creature, supernatural being, otherworldly presence, alien horror",
        "Pastierka husí": "goose shepherdess, rural horror, pastoral nightmare, countryside terror, agricultural supernatural",
        "Perníková chalúpka": "gingerbread house, fairy tale horror, candy cottage, witch's trap, sweet nightmare, confectionery terror",
        "Piatko a Pustaj": "Friday and Release, temporal horror, day-specific terror, calendar nightmare, time-based supernatural",
        "Pijan": "drunkard horror, alcohol nightmare, intoxication terror, substance abuse horror, drinking supernatural",
        "Posledné vysielanie": "last broadcast, media horror, transmission terror, radio nightmare, communication supernatural, final signal",
        "Poviedky podľa Marka": "stories according to Mark, narrative horror, storytelling terror, literary nightmare, tale supernatural",
        "Prečo sa už nevenujem jaskyniarstvu": "why I no longer do caving, underground horror, cave terror, spelunking nightmare, subterranean fear",
        "Prekliaty princ": "cursed prince, royal horror, aristocratic nightmare, noble curse, princely terror, regal supernatural",
        "Príbeh klaviristu Abrahama Granda": "pianist Abraham Grand story, musical horror, piano nightmare, keyboard terror, musical supernatural",
        "Prišli v maskách": "they came in masks, masked horror, disguise terror, identity nightmare, face-covering fear, anonymous threat",
        "Pustý hrad": "desolate castle, abandoned fortress, ruined stronghold, empty citadel, deserted palace, castle decay horror",
        "Rastliny": "plants horror, botanical nightmare, vegetation terror, flora supernatural, garden horror, plant possession",
        "Šarkanove deti": "dragon's children, draconic offspring, reptilian horror, monster progeny, serpentine nightmare, dragon spawn",
        "Šarlátové oči": "scarlet eyes, red eye horror, crimson gaze, blood-colored vision, ruby eye terror, red sight nightmare",
        "Sellendorf": "Sellendorf location horror, place-specific terror, geographical nightmare, location supernatural, regional horror",
        "Škofja Loka": "Škofja Loka Slovenia, Slovenian horror, alpine terror, mountain town nightmare, Central European supernatural",
        "Skutočný príbeh srbských upírov": "true story of Serbian vampires, historical vampire account, Balkan undead, documented horror, vampire epidemic",
        "Slavošovský tunel": "Slavošov tunnel, underground passage, subterranean horror, tunnel nightmare, enclosed space terror",
        "Slnko, Mesiac a Tália": "Sun, Moon and Talia, celestial horror, astronomical nightmare, cosmic terror, heavenly supernatural",
        "Spomienky starého ducha": "memories of old ghost, ancient spirit, spectral recollections, ghostly nostalgia, supernatural reminiscence",
        "Tanec svätého Víta": "Saint Vitus dance, medieval plague, dancing mania, historical horror, religious terror, mass hysteria",
        "Tichá voda": "silent water, still lake horror, quiet aquatic terror, motionless water nightmare, calm surface fear",
        "Tisíc rytierov": "thousand knights, medieval army horror, warrior nightmare, military supernatural, knightly terror, cavalry horror",
        "Trebišovské horory": "Trebišov horrors, regional terror, local nightmare, geographical horror, Slovak town supernatural",
        "Upír fekišovský": "Fekišov vampire, local undead, regional bloodsucker, village vampire, Slovak vampire legend",
        "Útulňa": "shelter horror, refuge nightmare, sanctuary terror, protection facility horror, safe house supernatural",
        "Vytie": "howling horror, wolf cry, animal terror, wilderness nightmare, predator sound, lupine supernatural",
        "Za starým dubom": "behind old oak, tree horror, forest nightmare, woodland terror, ancient oak supernatural, sylvan fear",
        "Zakliata hora": "cursed mountain, enchanted peak, mountain curse, alpine horror, highland supernatural, summit nightmare",
        "Záznamy z očistca": "purgatory records, afterlife documentation, limbo horror, spiritual realm terror, religious supernatural",
        "Zberačka jahôd": "strawberry picker, berry harvest horror, fruit gathering nightmare, agricultural terror, seasonal supernatural",
        "Zbohom svet": "goodbye world, apocalyptic horror, end times terror, final farewell nightmare, world ending supernatural",
        "Život pred životom": "life before life, reincarnation horror, past life terror, spiritual nightmare, soul supernatural",
        "Zmiznutí rodičia": "disappeared parents, missing family, parental horror, family nightmare, domestic terror, household supernatural",
        "Zmok (bez kostola a umývania)": "dragon without church and washing, Slovak dragon folklore, mythological beast, medieval monster"
    }

    # Hľadanie presného názvu alebo čiastočnej zhody
    for story_name, specific_prompt in prompts_map.items():
        if story_name.lower() in clean_name.lower() or clean_name.lower() in story_name.lower():
            return base_prompt + specific_prompt

    # Fallback pre neznáme príbehy
    return base_prompt + f"mysterious dark scene inspired by '{clean_name}', horror atmosphere, gothic elements, cinematic composition, supernatural terror"

def generate_image_with_gradio(prompt, filename_base):
    """Generuje obrázok pomocou Gradio Midjourney API"""
    try:
        print_color(Colors.BLUE, f"  Pripájam sa k Midjourney API...")
        client = Client("Dagfinn1962/Midjourney-Free")
        
        # Negatívny prompt pre lepšiu kvalitu
        negative_prompt = "(deformed iris, deformed pupils, semi-realistic, cgi, 3d, render, sketch, cartoon, drawing, anime:1.4), text, close up, cropped, out of frame, worst quality, low quality, jpeg artifacts, ugly, duplicate, morbid, mutilated, extra fingers, mutated hands, poorly drawn hands, poorly drawn face, mutation, deformed, blurry, dehydrated, bad anatomy, bad proportions, extra limbs, cloned face, disfigured, gross proportions, malformed limbs, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, long neck"
        
        print_color(Colors.BLUE, f"  Generujem obrázok...")
        print_color(Colors.CYAN, f"  Prompt: {prompt}")
        
        result = client.predict(
            prompt=prompt,
            negative_prompt=negative_prompt,
            use_negative_prompt=True,
            style="2560 x 1440",  # 16:9 pomer
            seed=0,
            width=1024,
            height=576,  # 16:9 pomer pre 1024 šírku
            guidance_scale=6,
            randomize_seed=True,
            api_name="/run"
        )
        
        print_color(Colors.GREEN, f"  ✓ API volanie úspešné")
        return result
        
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri generovaní: {e}")
        return None

def download_image(image_url, output_path):
    """Stiahne obrázok z URL"""
    try:
        print_color(Colors.BLUE, f"  Sťahujem obrázok...")
        response = requests.get(image_url, timeout=30)
        if response.status_code == 200:
            with open(output_path, 'wb') as f:
                f.write(response.content)
            print_color(Colors.GREEN, f"  ✓ Obrázok uložený: {os.path.basename(output_path)}")
            return True
        else:
            print_color(Colors.RED, f"  ✗ Chyba pri sťahovaní: HTTP {response.status_code}")
            return False
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri sťahovaní: {e}")
        return False

def create_video_with_image(mp3_file, image_file, output_file):
    """Vytvorí video z MP3 a obrázka pomocou FFmpeg"""
    print_color(Colors.BLUE, f"  Vytváram video...")
    
    cmd = [
        'ffmpeg',
        '-loop', '1',
        '-i', image_file,
        '-i', mp3_file,
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-vf', 'scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2',
        '-shortest',
        '-y', output_file,
        '-loglevel', 'error'
    ]
    
    import subprocess
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_color(Colors.GREEN, f"  ✓ Video vytvorené: {os.path.basename(output_file)}")
            return True
        else:
            print_color(Colors.RED, f"  ✗ Chyba pri vytváraní videa: {result.stderr}")
            return False
    except Exception as e:
        print_color(Colors.RED, f"  ✗ Chyba pri spúšťaní FFmpeg: {e}")
        return False

def main():
    print_color(Colors.PURPLE, "=== Gradio Midjourney Image & Video Generator ===")
    print_color(Colors.CYAN, "Generuje AI obrázky a videá pre horror audio súbory")
    print()
    
    # Nastavenia
    INPUT_DIR = os.path.expanduser("~/Desktop/Krvavý Audio")
    IMAGES_DIR = "./Midjourney_Images_16_9"
    VIDEOS_DIR = "./Midjourney_Videos"
    
    # Vytvorenie výstupných priečinkov
    os.makedirs(IMAGES_DIR, exist_ok=True)
    os.makedirs(VIDEOS_DIR, exist_ok=True)
    
    print_color(Colors.YELLOW, f"Vstupný priečinok: {INPUT_DIR}")
    print_color(Colors.YELLOW, f"Obrázky: {IMAGES_DIR}")
    print_color(Colors.YELLOW, f"Videá: {VIDEOS_DIR}")
    print()
    
    # Získanie zoznamu MP3 súborov
    if not os.path.exists(INPUT_DIR):
        print_color(Colors.RED, f"Priečinok {INPUT_DIR} neexistuje!")
        return
    
    mp3_files = [f for f in os.listdir(INPUT_DIR) if f.endswith('.mp3')]
    if not mp3_files:
        print_color(Colors.RED, f"Nenašli sa žiadne MP3 súbory v {INPUT_DIR}")
        return
    
    print_color(Colors.GREEN, f"Našiel som {len(mp3_files)} MP3 súborov")
    print()
    
    # Menu
    print_color(Colors.BLUE, "Vyberte možnosť:")
    print("1) Generovať len obrázky")
    print("2) Generovať obrázky + videá")
    print("3) Test s jedným súborom")
    print()
    choice = input("Zadajte číslo (1-3): ").strip()
    
    if choice == '3':
        # Test s prvým súborom
        mp3_files = mp3_files[:1]
        print_color(Colors.YELLOW, f"Testujem s: {mp3_files[0]}")
        print()
    
    # Spracovanie súborov
    success_count = 0
    for i, mp3_file in enumerate(mp3_files, 1):
        filename_base = mp3_file.replace('.mp3', '')
        mp3_path = os.path.join(INPUT_DIR, mp3_file)
        image_path = os.path.join(IMAGES_DIR, f"{filename_base}.jpg")
        video_path = os.path.join(VIDEOS_DIR, f"{filename_base}.mp4")
        
        print_color(Colors.YELLOW, f"[{i}/{len(mp3_files)}] {filename_base}")
        
        # Generovanie obrázka
        if not os.path.exists(image_path):
            prompt = create_horror_prompt(mp3_file)
            result = generate_image_with_gradio(prompt, filename_base)
            
            if result:
                # Spracovanie výsledku z Gradio API
                image_path_found = None

                if isinstance(result, tuple) and len(result) >= 1:
                    # Výsledok je tuple, prvý element obsahuje obrázky
                    images_data = result[0]
                    if isinstance(images_data, list) and len(images_data) > 0:
                        # Berieme prvý obrázok
                        first_image = images_data[0]
                        if isinstance(first_image, dict) and 'image' in first_image:
                            image_path_found = first_image['image']
                        elif isinstance(first_image, str):
                            image_path_found = first_image
                elif isinstance(result, str):
                    image_path_found = result

                if image_path_found:
                    print_color(Colors.BLUE, f"  Našiel som obrázok: {os.path.basename(image_path_found)}")

                    if image_path_found.startswith('http'):
                        # Je to URL, stiahni obrázok
                        if download_image(image_path_found, image_path):
                            success_count += 1
                        else:
                            print_color(Colors.RED, f"  ✗ Nepodarilo sa stiahnuť obrázok")
                            continue
                    else:
                        # Je to lokálna cesta
                        if os.path.exists(image_path_found):
                            import shutil
                            shutil.copy2(image_path_found, image_path)
                            print_color(Colors.GREEN, f"  ✓ Obrázok skopírovaný")
                            success_count += 1
                        else:
                            print_color(Colors.RED, f"  ✗ Obrázok sa nenašiel na: {image_path_found}")
                            continue
                else:
                    print_color(Colors.RED, f"  ✗ Nepodarilo sa extrahovať cestu k obrázku z výsledku")
                    print_color(Colors.YELLOW, f"  Debug: {str(result)[:200]}...")
                    continue
            else:
                print_color(Colors.RED, f"  ✗ Nepodarilo sa vygenerovať obrázok")
                continue
        else:
            print_color(Colors.GREEN, f"  ✓ Obrázok už existuje")
            success_count += 1
        
        # Vytvorenie videa (ak je požadované)
        if choice in ['2', '3'] and os.path.exists(image_path):
            if not os.path.exists(video_path):
                if create_video_with_image(mp3_path, image_path, video_path):
                    print_color(Colors.GREEN, f"  ✓ Kompletné!")
                else:
                    print_color(Colors.RED, f"  ✗ Video sa nepodarilo vytvoriť")
            else:
                print_color(Colors.GREEN, f"  ✓ Video už existuje")
        
        print()
        
        # Pauza medzi požiadavkami (aby sme nepretažili API)
        if i < len(mp3_files):
            print_color(Colors.BLUE, "  Čakám 3 sekundy...")
            time.sleep(3)
    
    print_color(Colors.GREEN, "=== Spracovanie dokončené! ===")
    print_color(Colors.YELLOW, f"Úspešne spracovaných: {success_count}/{len(mp3_files)} súborov")
    
    if choice in ['2', '3']:
        print_color(Colors.BLUE, f"Obrázky: {IMAGES_DIR}")
        print_color(Colors.BLUE, f"Videá: {VIDEOS_DIR}")
    else:
        print_color(Colors.BLUE, f"Obrázky: {IMAGES_DIR}")

if __name__ == "__main__":
    # Kontrola závislostí
    try:
        from gradio_client import Client
        import requests
    except ImportError as e:
        print_color(Colors.RED, f"Chýbajú závislosti: {e}")
        print_color(Colors.YELLOW, "Nainštalujte ich pomocou:")
        print_color(Colors.BLUE, "pip install gradio_client requests")
        sys.exit(1)
    
    main()
