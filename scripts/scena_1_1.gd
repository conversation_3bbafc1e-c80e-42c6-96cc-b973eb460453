extends Node2D

# Scéna 1.1 - Základná scéna pre Prekliateho Dedičstvo

# Správne riešenie hlavolamu
const SPRAVNE_RIESENIE = "GROFKAJEVKRYPTE"

# Called when the node enters the scene tree for the first time.
func _ready():
	print("Scéna 1.1 je načítaná")

	# Jednoduché pripojenie sign<PERSON>lov
	if has_node("StartButton"):
		$StartButton.pressed.connect(_on_start_button_pressed)

	if has_node("UI_Hlavolam/TlacitkoOverit"):
		$UI_Hlavolam/TlacitkoOverit.pressed.connect(_on_tlacitko_overit_pressed)

	# UI je na začiatku skryté
	if has_node("UI_Hlavolam"):
		$UI_Hlavolam.visible = false

	print("Scéna je pripravená")

func _on_start_button_pressed():
	print("Spúšťa sa hádanka...")
	if has_node("StartButton"):
		$StartButton.visible = false
	if has_node("UI_Hlavolam"):
		$UI_Hlavolam.visible = true

func _on_tlacitko_overit_pressed():
	print("Overujem odpoveď...")

	if not has_node("UI_Hlavolam/VstupnePole"):
		print("Chyba: VstupnePole sa nenašlo")
		return

	var odpoved = $UI_Hlavolam/VstupnePole.text
	var normalizovana_odpoved = _normalizuj_text(odpoved)

	if normalizovana_odpoved == SPRAVNE_RIESENIE:
		print("Správna odpoveď!")
		if has_node("UI_Hlavolam"):
			$UI_Hlavolam.visible = false
		_start_end_sequence()
	else:
		print("Nesprávna odpoveď: " + normalizovana_odpoved)
		$UI_Hlavolam/VstupnePole.text = ""

func _normalizuj_text(text: String) -> String:
	# Odstráni diakritiku, medzery a prevedie na veľké písmená
	var normalizovany = text.to_upper()
	normalizovany = normalizovany.replace(" ", "")

	# Odstránenie diakritiky
	var diakritika = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}

	for diakr in diakritika:
		normalizovany = normalizovany.replace(diakr, diakritika[diakr])

	return normalizovany

func _start_end_sequence():
	print("Spúšťa sa koncová sekvencia...")

	# Blesk efekt s animáciou
	var tween = create_tween()
	tween.tween_property(blesk, "color:a", 1.0, 0.05)
	tween.tween_property(blesk, "color:a", 0.0, 0.05)

	# Simulácia hromu pomocí screen shake
	_screen_shake()

	await get_tree().create_timer(0.2).timeout

	# Zastaviť ambientný zvuk
	zvuk_ambient.stop()

	# Simulácia paniky koní pomocí rýchlej animácie kočiara
	_animate_carriage_panic()

	# Krátka pauza
	await get_tree().create_timer(2.0).timeout

	print("Kočiš: 'Čo to bolo?! Kone sa vyplašili!'")
	await get_tree().create_timer(2.0).timeout

	# Prechod na ďalšiu scénu
	SceneManager.change_scene("res://scenes/scena_1_2.tscn")

func _screen_shake():
	var kociar_grafika = $Kociar_Grafika
	var original_pos = kociar_grafika.position
	var tween = create_tween()
	tween.set_loops(5)
	tween.tween_property(kociar_grafika, "position", original_pos + Vector2(10, 0), 0.05)
	tween.tween_property(kociar_grafika, "position", original_pos + Vector2(-10, 0), 0.05)
	tween.tween_callback(func(): kociar_grafika.position = original_pos)

func _animate_carriage_panic():
	var kociar_grafika = $Kociar_Grafika
	var tween = create_tween()
	tween.set_loops(3)
	tween.tween_property(kociar_grafika, "rotation", 0.1, 0.2)
	tween.tween_property(kociar_grafika, "rotation", -0.1, 0.2)
	tween.tween_callback(func(): kociar_grafika.rotation = 0)
