extends Node2D

# Jednoduchá verzia scény 1.1

const SPRAVNE_RIESENIE = "GROFKAJEVKRYPTE"

func _ready():
	print("Scéna 1.1 je načítaná")
	
	# <PERSON>ripo<PERSON><PERSON> sign<PERSON>lov
	if has_node("StartButton"):
		$StartButton.pressed.connect(_on_start_button_pressed)
	
	if has_node("UI_Hlavolam/TlacitkoOverit"):
		$UI_Hlavolam/TlacitkoOverit.pressed.connect(_on_tlacitko_overit_pressed)
	
	# UI je na začiatku skryté
	if has_node("UI_Hlavolam"):
		$UI_Hlavolam.visible = false

func _on_start_button_pressed():
	print("Spúšťa sa hádanka...")
	$StartButton.visible = false
	$UI_Hlavolam.visible = true

func _on_tlacitko_overit_pressed():
	print("Overujem odpoveď...")
	
	var odpoved = $UI_Hlavolam/VstupnePole.text
	var normalizovana_odpoved = _normalizuj_text(odpoved)
	
	if normalizovana_odpoved == SPRAVNE_RIESENIE:
		print("Správna odpoveď!")
		$UI_Hlavolam.visible = false
		print("Prechod na ďalšiu scénu...")
		await get_tree().create_timer(2.0).timeout
		get_tree().change_scene_to_file("res://scenes/scena_1_2.tscn")
	else:
		print("Nesprávna odpoveď: " + normalizovana_odpoved)
		$UI_Hlavolam/VstupnePole.text = ""

func _normalizuj_text(text: String) -> String:
	var normalizovany = text.to_upper()
	normalizovany = normalizovany.replace(" ", "")
	
	# Odstránenie diakritiky
	var diakritika = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ý": "Y", "Ž": "Z"
	}
	
	for diakr in diakritika:
		normalizovany = normalizovany.replace(diakr, diakritika[diakr])
	
	return normalizovany
