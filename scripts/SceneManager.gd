extends Node

# SceneManager - Globálny správca scén pre plynulé prechody
# Singleton/Autoload skript

var fade_rect: ColorRect
var is_transitioning: bool = false

func _ready():
	# Vytvorenie fade overlay
	_create_fade_overlay()

func _create_fade_overlay():
	# Vytvorenie CanvasLayer pre fade efekt
	var canvas_layer = CanvasLayer.new()
	canvas_layer.layer = 100  # Najvyššia vrstva
	add_child(canvas_layer)
	
	# Vytvorenie ColorRect pre fade efekt
	fade_rect = ColorRect.new()
	fade_rect.color = Color.BLACK
	fade_rect.color.a = 0.0  # Začína priehľadne
	fade_rect.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	canvas_layer.add_child(fade_rect)

func change_scene(scene_path: String, fade_duration: float = 1.0):
	if is_transitioning:
		return
	
	is_transitioning = true
	
	# Fade out
	await _fade_to_black(fade_duration / 2.0)
	
	# <PERSON>mena scény
	get_tree().change_scene_to_file(scene_path)
	
	# Kr<PERSON>tka pauza pre načítanie scény
	await get_tree().create_timer(0.1).timeout
	
	# Fade in
	await _fade_from_black(fade_duration / 2.0)
	
	is_transitioning = false

func _fade_to_black(duration: float):
	var tween = create_tween()
	tween.tween_property(fade_rect, "color:a", 1.0, duration)
	await tween.finished

func _fade_from_black(duration: float):
	var tween = create_tween()
	tween.tween_property(fade_rect, "color:a", 0.0, duration)
	await tween.finished
