#!/bin/bash

# Kompletný workflow: MP3 → Obrázky 16:9 → Videá
# Automatizuje celý proces od generovania obrázkov po vytvorenie videí

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

print_header() {
    echo ""
    print_color $CYAN "=================================="
    print_color $CYAN "$1"
    print_color $CYAN "=================================="
    echo ""
}

# Kontrola požiadaviek
check_requirements() {
    print_header "Kontrola požiadaviek"
    
    local missing=0
    
    if ! command -v ffmpeg &> /dev/null; then
        print_color $RED "✗ FFmpeg nie je nainštalovaný"
        missing=1
    else
        print_color $GREEN "✓ FFmpeg nainštalovaný"
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_color $YELLOW "⚠ Python3 nie je nainštalovaný (potrebný pre pokročilé obrázky)"
    else
        print_color $GREEN "✓ Python3 nainštalovaný"
    fi
    
    # Kontrola MP3 súborov
    INPUT_DIR="$HOME/Desktop/Krvavý Audio"
    if [ -d "$INPUT_DIR" ]; then
        mp3_count=$(find "$INPUT_DIR" -name "*.mp3" | wc -l)
        print_color $GREEN "✓ Našiel som $mp3_count MP3 súborov"
    else
        print_color $RED "✗ Priečinok 'Krvavý Audio' neexistuje"
        missing=1
    fi
    
    if [ $missing -eq 1 ]; then
        print_color $RED "Niektoré požiadavky chýbajú. Opravte ich a spustite znovu."
        exit 1
    fi
}

# Hlavné menu
show_main_menu() {
    print_header "MP3 → Video Workflow"
    print_color $BLUE "Vyberte workflow:"
    echo ""
    echo "🎨 GENEROVANIE OBRÁZKOV:"
    echo "  1) Jednoduché pozadia 16:9 (rýchle)"
    echo "  2) Pokročilé obrázky s textom (Python)"
    echo "  3) OpenAI DALL-E obrázky (potrebný API kľúč)"
    echo "  4) 🆕 Gradio Midjourney AI obrázky (NOVÉ!)"
    echo ""
    echo "🎬 VYTVORENIE VIDEÍ:"
    echo "  5) Rýchla konverzia (tmavo červené pozadie)"
    echo "  6) Spektrogram videá (efektné)"
    echo "  7) Interaktívny konverter (všetky možnosti)"
    echo ""
    echo "🚀 KOMPLETNÝ WORKFLOW:"
    echo "  8) Pozadia + Rýchle videá"
    echo "  9) Pozadia + Spektrogram videá"
    echo "  10) Python obrázky + Videá s obrázkami"
    echo "  11) 🆕 Midjourney AI obrázky + Videá (NAJLEPŠIE!)"
    echo ""
    echo "📊 INFORMÁCIE:"
    echo "  12) Zobraziť štatistiky"
    echo "  0) Ukončiť"
    echo ""
}

# Štatistiky
show_statistics() {
    print_header "Štatistiky projektu"
    
    INPUT_DIR="$HOME/Desktop/Krvavý Audio"
    mp3_count=$(find "$INPUT_DIR" -name "*.mp3" 2>/dev/null | wc -l)
    
    print_color $YELLOW "📁 MP3 súbory:"
    print_color $BLUE "  • Počet: $mp3_count súborov"
    
    if [ -d "./Horror_Images_16_9" ]; then
        img_count=$(find "./Horror_Images_16_9" -name "*.png" 2>/dev/null | wc -l)
        print_color $YELLOW "🎨 Vygenerované obrázky:"
        print_color $BLUE "  • Počet: $img_count obrázkov"
    fi
    
    if [ -d "./Simple_Backgrounds_16_9" ]; then
        bg_count=$(find "./Simple_Backgrounds_16_9" -name "*.png" 2>/dev/null | wc -l)
        print_color $YELLOW "🖼️ Jednoduché pozadia:"
        print_color $BLUE "  • Počet: $bg_count pozadí"
    fi
    
    if [ -d "./Krvavý_Audio_Videos" ]; then
        video_count=$(find "./Krvavý_Audio_Videos" -name "*.mp4" 2>/dev/null | wc -l)
        print_color $YELLOW "🎬 Vytvorené videá:"
        print_color $BLUE "  • Počet: $video_count videí"
    fi
    
    if [ -d "./Krvavý_Audio_Spectrum_Videos" ]; then
        spectrum_count=$(find "./Krvavý_Audio_Spectrum_Videos" -name "*.mp4" 2>/dev/null | wc -l)
        print_color $YELLOW "📊 Spektrogram videá:"
        print_color $BLUE "  • Počet: $spectrum_count videí"
    fi

    if [ -d "./Midjourney_Images_16_9" ]; then
        midjourney_img_count=$(find "./Midjourney_Images_16_9" -name "*.jpg" 2>/dev/null | wc -l)
        print_color $YELLOW "🤖 Midjourney AI obrázky:"
        print_color $BLUE "  • Počet: $midjourney_img_count obrázkov"
    fi

    if [ -d "./Midjourney_Videos" ]; then
        midjourney_video_count=$(find "./Midjourney_Videos" -name "*.mp4" 2>/dev/null | wc -l)
        print_color $YELLOW "🎬 Midjourney AI videá:"
        print_color $BLUE "  • Počet: $midjourney_video_count videí"
    fi
    
    echo ""
    print_color $GREEN "Odhadovaný čas spracovania:"
    print_color $BLUE "  • Rýchle videá: $((mp3_count * 2)) - $((mp3_count * 4)) minút"
    print_color $BLUE "  • Spektrogram videá: $((mp3_count * 4)) - $((mp3_count * 8)) minút"
}

# Workflow funkcie
workflow_simple_backgrounds() {
    print_header "Vytváram jednoduché pozadia"
    ./create_simple_backgrounds.sh
}

workflow_python_images() {
    print_header "Vytváram pokročilé obrázky"
    if command -v python3 &> /dev/null; then
        python3 generate_16_9_images.py
    else
        print_color $RED "Python3 nie je nainštalovaný!"
        return 1
    fi
}

workflow_midjourney_images() {
    print_header "Vytváram Midjourney AI obrázky"
    if command -v python3 &> /dev/null; then
        python3 gradio_midjourney_generator.py
    else
        print_color $RED "Python3 nie je nainštalovaný!"
        return 1
    fi
}

workflow_quick_videos() {
    print_header "Vytváram rýchle videá"
    ./quick_mp3_to_video.sh
}

workflow_spectrum_videos() {
    print_header "Vytváram spektrogram videá"
    ./spectrum_video_converter.sh
}

workflow_interactive_converter() {
    print_header "Spúšťam interaktívny konverter"
    ./mp3_to_video_converter.sh
}

# Kompletné workflow
workflow_complete_simple() {
    print_header "Kompletný workflow: Pozadia + Rýchle videá"
    
    print_color $BLUE "Krok 1/2: Vytváram jednoduché pozadia..."
    echo "7" | ./create_simple_backgrounds.sh  # Všetky typy pozadí
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ Pozadia vytvorené"
        print_color $BLUE "Krok 2/2: Vytváram videá..."
        ./quick_mp3_to_video.sh
    else
        print_color $RED "✗ Chyba pri vytváraní pozadí"
    fi
}

workflow_complete_spectrum() {
    print_header "Kompletný workflow: Pozadia + Spektrogram videá"
    
    print_color $BLUE "Krok 1/2: Vytváram jednoduché pozadia..."
    echo "7" | ./create_simple_backgrounds.sh
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ Pozadia vytvorené"
        print_color $BLUE "Krok 2/2: Vytváram spektrogram videá..."
        echo "1" | ./spectrum_video_converter.sh  # Dúhový spektrogram
    else
        print_color $RED "✗ Chyba pri vytváraní pozadí"
    fi
}

workflow_complete_python() {
    print_header "Kompletný workflow: Python obrázky + Videá"

    print_color $BLUE "Krok 1/2: Vytváram pokročilé obrázky..."
    if command -v python3 &> /dev/null; then
        python3 generate_16_9_images.py

        if [ $? -eq 0 ]; then
            print_color $GREEN "✓ Obrázky vytvorené"
            print_color $BLUE "Krok 2/2: Vytváram videá s obrázkami..."
            echo "7" | ./mp3_to_video_converter.sh  # Auto-použitie vygenerovaných obrázkov
        else
            print_color $RED "✗ Chyba pri vytváraní obrázkov"
        fi
    else
        print_color $RED "Python3 nie je nainštalovaný!"
    fi
}

workflow_complete_midjourney() {
    print_header "Kompletný workflow: Midjourney AI obrázky + Videá"

    print_color $BLUE "Krok 1/2: Vytváram AI obrázky pomocou Midjourney..."
    if command -v python3 &> /dev/null; then
        echo "2" | python3 gradio_midjourney_generator.py  # Obrázky + videá

        if [ $? -eq 0 ]; then
            print_color $GREEN "✓ AI obrázky a videá vytvorené!"
            print_color $CYAN "🎉 Midjourney workflow dokončený!"
        else
            print_color $RED "✗ Chyba pri vytváraní AI obsahu"
        fi
    else
        print_color $RED "Python3 nie je nainštalovaný!"
    fi
}

# Hlavná slučka
main() {
    # Kontrola požiadaviek
    check_requirements
    
    # Urobenie skriptov spustiteľnými
    chmod +x *.sh 2>/dev/null
    
    while true; do
        show_main_menu
        read -p "Zadajte voľbu: " choice
        
        case $choice in
            1) workflow_simple_backgrounds ;;
            2) workflow_python_images ;;
            3)
                print_color $BLUE "Spúšťam Python generátor s OpenAI možnosťou..."
                python3 generate_16_9_images.py
                ;;
            4) workflow_midjourney_images ;;
            5) workflow_quick_videos ;;
            6) workflow_spectrum_videos ;;
            7) workflow_interactive_converter ;;
            8) workflow_complete_simple ;;
            9) workflow_complete_spectrum ;;
            10) workflow_complete_python ;;
            11) workflow_complete_midjourney ;;
            12) show_statistics ;;
            0)
                print_color $GREEN "Ďakujem za použitie MP3 → Video Workflow!"
                exit 0
                ;;
            *)
                print_color $RED "Neplatná voľba. Skúste znovu."
                ;;
        esac
        
        echo ""
        print_color $YELLOW "Stlačte Enter pre pokračovanie..."
        read
    done
}

# Spustenie
main "$@"
