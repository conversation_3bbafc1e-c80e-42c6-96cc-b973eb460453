#!/bin/bash

# Rýchla konverzia MP3 na video s tmavo červeným pozadím
# Pre horror audio súbory z priečinka "Krvavý Audio"

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

# Kontrola FFmpeg
if ! command -v ffmpeg &> /dev/null; then
    print_color $RED "FFmpeg nie je nainštalovaný!"
    print_color $YELLOW "Nainštalujte ho pomocou: brew install ffmpeg"
    exit 1
fi

# Nastavenia
INPUT_DIR="$HOME/Desktop/Krvavý Audio"
OUTPUT_DIR="./Krvavý_Audio_Videos"
RESOLUTION="1920x1080"
BG_COLOR="0x1a0000"  # Tmavo červená farba

# Vytvorenie výstupného priečinka
mkdir -p "$OUTPUT_DIR"

print_color $GREEN "=== Rýchla konverzia MP3 → Video ==="
print_color $YELLOW "Vstup: $INPUT_DIR"
print_color $YELLOW "Výstup: $OUTPUT_DIR"
print_color $YELLOW "Pozadie: Tmavo červené"
print_color $YELLOW "Rozlíšenie: $RESOLUTION"
echo ""

# Spočítanie súborov
total=$(find "$INPUT_DIR" -name "*.mp3" | wc -l)
print_color $GREEN "Našiel som $total MP3 súborov"
echo ""

# Spracovanie
count=0
find "$INPUT_DIR" -name "*.mp3" | while read -r mp3_file; do
    count=$((count + 1))
    filename=$(basename "$mp3_file" .mp3)
    output_file="$OUTPUT_DIR/${filename}.mp4"
    
    print_color $YELLOW "[$count/$total] $filename"
    
    # Vytvorenie videa s tmavo červeným pozadím
    ffmpeg -i "$mp3_file" \
           -f lavfi -i "color=c=$BG_COLOR:size=$RESOLUTION:rate=25" \
           -c:v libx264 -c:a aac \
           -shortest \
           -y "$output_file" \
           -loglevel error
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ Hotovo"
    else
        print_color $RED "✗ Chyba"
    fi
done

print_color $GREEN ""
print_color $GREEN "=== Konverzia dokončená! ==="
print_color $YELLOW "Videá nájdete v: $OUTPUT_DIR"
