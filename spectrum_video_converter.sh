#!/bin/bash

# Spektrogram video konverter
# Vytvorí krásne spektrogram videá z MP3 súborov

# Farby
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_color() {
    echo -e "${1}${2}${NC}"
}

# Kontrola FFmpeg
if ! command -v ffmpeg &> /dev/null; then
    print_color $RED "FFmpeg nie je nainštalovaný!"
    print_color $YELLOW "Nainštalujte ho pomocou: brew install ffmpeg"
    exit 1
fi

# Nastavenia
INPUT_DIR="$HOME/Desktop/Krvavý Audio"
OUTPUT_DIR="./Krvavý_Audio_Spectrum_Videos"
RESOLUTION="1920x1080"

# Vytvorenie výstupného priečinka
mkdir -p "$OUTPUT_DIR"

print_color $PURPLE "=== Spektrogram Video Konverter ==="
print_color $YELLOW "Vstup: $INPUT_DIR"
print_color $YELLOW "Výstup: $OUTPUT_DIR"
print_color $YELLOW "Rozlíšenie: $RESOLUTION"
echo ""

# Menu pre typ spektrogramu
print_color $BLUE "Vyberte typ spektrogramu:"
echo "1) Klasický spektrogram (dúhové farby)"
echo "2) Tmavý spektrogram (červeno-oranžové farby)"
echo "3) Horror spektrogram (červeno-čierne farby)"
echo "4) Waveform + spektrogram kombinácia"
echo ""
read -p "Zadajte číslo (1-4): " choice

# Nastavenie filtrov podľa výberu
case $choice in
    1)
        FILTER="[0:a]showspectrum=s=$RESOLUTION:mode=combined:color=rainbow:scale=log,format=yuv420p[v]"
        TYPE="rainbow"
        ;;
    2)
        FILTER="[0:a]showspectrum=s=$RESOLUTION:mode=combined:color=fire:scale=log,format=yuv420p[v]"
        TYPE="fire"
        ;;
    3)
        FILTER="[0:a]showspectrum=s=$RESOLUTION:mode=combined:color=cool:scale=log,format=yuv420p[v]"
        TYPE="horror"
        ;;
    4)
        FILTER="[0:a]showspectrum=s=1920x540:mode=combined:color=rainbow:scale=log[spec];[0:a]showwaves=s=1920x540:mode=line:colors=white[waves];[spec][waves]vstack=inputs=2,format=yuv420p[v]"
        TYPE="combined"
        ;;
    *)
        print_color $RED "Neplatná voľba, používam dúhový spektrogram"
        FILTER="[0:a]showspectrum=s=$RESOLUTION:mode=combined:color=rainbow:scale=log,format=yuv420p[v]"
        TYPE="rainbow"
        ;;
esac

# Spočítanie súborov
total=$(find "$INPUT_DIR" -name "*.mp3" | wc -l)
print_color $GREEN "Našiel som $total MP3 súborov"
print_color $BLUE "Typ spektrogramu: $TYPE"
echo ""

# Spracovanie
count=0
find "$INPUT_DIR" -name "*.mp3" | while read -r mp3_file; do
    count=$((count + 1))
    filename=$(basename "$mp3_file" .mp3)
    output_file="$OUTPUT_DIR/${filename}_spectrum.mp4"
    
    print_color $YELLOW "[$count/$total] $filename"
    
    # Vytvorenie spektrogram videa
    ffmpeg -i "$mp3_file" \
           -filter_complex "$FILTER" \
           -map "[v]" -map 0:a \
           -c:v libx264 -c:a aac \
           -preset fast \
           -crf 18 \
           -y "$output_file" \
           -loglevel error
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✓ Spektrogram vytvorený"
    else
        print_color $RED "✗ Chyba pri vytváraní spektrogramu"
    fi
done

print_color $GREEN ""
print_color $PURPLE "=== Spektrogram videá dokončené! ==="
print_color $YELLOW "Videá nájdete v: $OUTPUT_DIR"
print_color $BLUE "Tip: Spektrogram videá sú ideálne pre YouTube a sociálne siete!"
